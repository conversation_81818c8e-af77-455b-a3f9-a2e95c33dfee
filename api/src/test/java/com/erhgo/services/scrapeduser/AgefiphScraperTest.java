package com.erhgo.services.scrapeduser;

import com.erhgo.AbstractIntegrationTest;
import com.erhgo.config.ResetDataAfter;
import com.erhgo.domain.exceptions.InvalidScrapingException;
import com.erhgo.services.http.RetryableHttpClient;
import jakarta.persistence.EntityManager;
import okhttp3.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.bean.override.mockito.MockitoBean;
import org.springframework.test.util.ReflectionTestUtils;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;


class AgefiphScraperTest extends AbstractIntegrationTest {

    @Autowired
    private AgefiphScraper agefiphScraper;

    @MockitoBean
    private RetryableHttpClient retryableHttpClient;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(agefiphScraper, "searchUrl", "https://espace-emploi.agefiph.fr/api/company/candidates/search");
        ReflectionTestUtils.setField(agefiphScraper, "baseUrl", "https://espace-emploi.agefiph.fr");

        txHelper.doInTransaction(() -> {
            var sql = "INSERT INTO ConfigurableProperty(propertyKey, propertyValue) VALUES ('agefiph.auth.token', 'tokenxxxxx')";
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });
    }

    @Test
    @ResetDataAfter
    void searchCandidates_should_return_parsed_candidates_with_minimal_data() {
        var responseJson = """
                {
                    "total": 1,
                    "items": [
                        {
                            "id": "12345",
                            "firstName": "John",
                            "name": "Doe",
                            "email": "<EMAIL>",
                            "title": "Software Developer",
                            "hasResume": true,
                            "addressLocation": {
                                "label": "Paris (75)"
                            }
                        }
                    ]
                }
                """;

        when(retryableHttpClient.executeRequestWithStatusCheck(any(Request.class)))
                .thenReturn(createMockResponse(responseJson));

        var candidates = agefiphScraper.searchCandidates();

        assertThat(candidates).hasSize(1);
        var candidate = candidates.getFirst();

        // RGPD compliance: Only minimal data should be available from search
        assertThat(candidate.getCandidateId()).isEqualTo("12345");
        assertThat(candidate.getEmail()).isEqualTo("<EMAIL>");
        assertThat(candidate.getCvDownloadLink()).isEqualTo("https://espace-emploi.agefiph.fr/api/company/candidates/12345/resume/download");
        assertThat(candidate.getProfileUrl()).isEqualTo("https://espace-emploi.agefiph.fr/api/company/candidates/12345");

        // Personal data should be available from search response but not stored during initial scraping
        assertThat(candidate.getFirstName()).isEqualTo("John");
        assertThat(candidate.getLastName()).isEqualTo("Doe");
        assertThat(candidate.getJobTitle()).isEqualTo("Software Developer");
        assertThat(candidate.getLocation()).isEqualTo("Paris (75)");
    }

    @Test
    @ResetDataAfter
    void searchCandidates_should_filter_candidates_without_resume() {
        var responseJson = """
                {
                    "total": 2,
                    "items": [
                        {
                            "id": "12345",
                            "firstName": "John",
                            "name": "Doe",
                            "email": "<EMAIL>",
                            "title": "Software Developer",
                            "hasResume": true,
                            "addressLocation": {
                                "label": "Paris (75)"
                            }
                        },
                        {
                            "id": "67890",
                            "firstName": "Jane",
                            "name": "Smith",
                            "email": "<EMAIL>",
                            "title": "Data Analyst",
                            "hasResume": false,
                            "addressLocation": {
                                "label": "Lyon (69)"
                            }
                        }
                    ]
                }
                """;

        when(retryableHttpClient.executeRequestWithStatusCheck(any(Request.class)))
                .thenReturn(createMockResponse(responseJson));

        var candidates = agefiphScraper.searchCandidates();

        // Should only return candidates with hasResume: true
        assertThat(candidates).hasSize(1);
        assertThat(candidates.getFirst().getCandidateId()).isEqualTo("12345");
        assertThat(candidates.getFirst().getEmail()).isEqualTo("<EMAIL>");
    }

    @Test
    @ResetDataAfter
    void searchCandidates_should_throw_exception_on_server_error() {
        when(retryableHttpClient.executeRequestWithStatusCheck(any()))
                .thenThrow(new RetryableHttpClient.HttpRetryableException("HTTP error: 500"));

        assertThatThrownBy(() -> agefiphScraper.searchCandidates())
                .isInstanceOf(InvalidScrapingException.class);
    }

    @Test
    @ResetDataAfter
    void getCandidateDetails_should_return_complete_candidate_data() {
        var candidateDetailsJson = """
                {
                    "id": "12345",
                    "firstName": "John",
                    "name": "Doe",
                    "login": "<EMAIL>",
                    "addressLocation": {
                        "label": "Paris (75)"
                    },
                    "jobs": [
                        {
                            "id": 2145,
                            "value": "Senior Software Developer",
                            "unambiguous": true
                        }
                    ],
                    "hasResume": true
                }
                """;

        when(retryableHttpClient.executeRequestWithStatusCheck(any(Request.class)))
                .thenReturn(createMockResponse(candidateDetailsJson));

        var candidate = agefiphScraper.getCandidateDetails("12345");

        assertThat(candidate).isNotNull();
        assertThat(candidate.getCandidateId()).isEqualTo("12345");
        assertThat(candidate.getFirstName()).isEqualTo("John");
        assertThat(candidate.getLastName()).isEqualTo("Doe");
        assertThat(candidate.getEmail()).isEqualTo("<EMAIL>");
        assertThat(candidate.getJobTitle()).isEqualTo("Senior Software Developer");
        assertThat(candidate.getLocation()).isEqualTo("Paris (75)");
        assertThat(candidate.getCvDownloadLink()).isEqualTo("https://espace-emploi.agefiph.fr/api/company/candidates/12345/resume/download");
        assertThat(candidate.getProfileUrl()).isEqualTo("https://espace-emploi.agefiph.fr/api/company/candidates/12345");
    }

    @Test
    @ResetDataAfter
    void getCandidateDetails_should_throw_exception_on_server_error() {
        when(retryableHttpClient.executeRequestWithStatusCheck(any()))
                .thenThrow(new RetryableHttpClient.HttpRetryableException("HTTP error: 500"));

        assertThatThrownBy(() -> agefiphScraper.getCandidateDetails("12345"))
                .isInstanceOf(InvalidScrapingException.class);
    }

    @Test
    @ResetDataAfter
    void downloadCv_should_return_pdf_bytes() {
        var mockPdfBytes = "Mock PDF content".getBytes();
        when(retryableHttpClient.executeRequestWithStatusCheck(any()))
                .thenReturn(createMockResponse(mockPdfBytes));

        var result = agefiphScraper.downloadCv("http://example.com/cv.pdf");

        assertThat(result).isEqualTo(mockPdfBytes);
    }

    @Test
    @ResetDataAfter
    void downloadCv_should_return_null_on_error() {
        when(retryableHttpClient.executeRequestWithStatusCheck(any()))
                .thenThrow(new RuntimeException("Error"));

        var result = agefiphScraper.downloadCv("http://example.com/cv.pdf");

        assertThat(result).isNull();
    }

    @Test
    @ResetDataAfter
    void searchCandidates_should_use_post_request_with_json_payload() {
        var responseJson = """
                {
                    "total": 0,
                    "items": []
                }
                """;

        when(retryableHttpClient.executeRequestWithStatusCheck(any(Request.class)))
                .thenReturn(createMockResponse(responseJson));

        agefiphScraper.searchCandidates();

        // Verify that a POST request was made (this would be captured by the mock)
        // The actual verification of the request method and payload would require
        // more sophisticated mocking, but the test ensures the method completes successfully
        // with the new JSON response format
    }

    @Test
    @ResetDataAfter
    void searchCandidates_should_return_empty_list_when_no_auth_token() {
        // Remove the auth token
        txHelper.doInTransaction(() -> {
            var sql = "DELETE FROM ConfigurableProperty WHERE propertyKey = 'agefiph.auth.token'";
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });

        var candidates = agefiphScraper.searchCandidates();

        assertThat(candidates).isEmpty();
    }

    @Test
    @ResetDataAfter
    void getCandidateDetails_should_handle_candidate_without_jobs() {
        var candidateDetailsJson = """
                {
                    "id": "12345",
                    "firstName": "John",
                    "name": "Doe",
                    "login": "<EMAIL>",
                    "addressLocation": {
                        "label": "Paris (75)"
                    },
                    "jobs": [],
                    "hasResume": true
                }
                """;

        when(retryableHttpClient.executeRequestWithStatusCheck(any(Request.class)))
                .thenReturn(createMockResponse(candidateDetailsJson));

        var candidate = agefiphScraper.getCandidateDetails("12345");

        assertThat(candidate).isNotNull();
        assertThat(candidate.getCandidateId()).isEqualTo("12345");
        assertThat(candidate.getJobTitle()).isEmpty(); // Should be empty string when no jobs
    }

    @Test
    @ResetDataAfter
    void getCandidateDetails_should_throw_exception_when_no_auth_token() {
        // Remove the auth token
        txHelper.doInTransaction(() -> {
            var sql = "DELETE FROM ConfigurableProperty WHERE propertyKey = 'agefiph.auth.token'";
            applicationContext.getBean(EntityManager.class).createNativeQuery(sql).executeUpdate();
        });

        assertThatThrownBy(() -> agefiphScraper.getCandidateDetails("12345"))
                .isInstanceOf(InvalidScrapingException.class)
                .hasMessageContaining("AGEFIPH auth token not configured");
    }

    private Response createMockResponse(String body) {
        var responseBody = ResponseBody.create(body, MediaType.parse("application/json"));
        return new Response.Builder()
                .request(new Request.Builder().url("https://example.com").build())
                .protocol(Protocol.HTTP_1_1)
                .code(200)
                .message("OK")
                .body(responseBody)
                .build();
    }

    private Response createMockResponse(byte[] responseBody) {
        var body = ResponseBody.create(responseBody, MediaType.parse("application/octet-stream"));
        return new Response.Builder()
                .request(new Request.Builder().url("https://example.com").build())
                .protocol(Protocol.HTTP_1_1)
                .code(200)
                .message("OK")
                .body(body)
                .build();
    }
}
