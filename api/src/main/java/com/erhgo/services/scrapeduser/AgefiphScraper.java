package com.erhgo.services.scrapeduser;

import com.erhgo.domain.exceptions.InvalidScrapingException;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.services.http.RetryableHttpClient;
import com.erhgo.services.scrapeduser.dto.ScrapedCandidate;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.StreamSupport;

@Slf4j
@Service
@RequiredArgsConstructor
public class AgefiphScraper {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final RetryableHttpClient retryableHttpClient;
    private final ConfigurablePropertyRepository configurablePropertyRepository;

    @Value("${agefiph.api.search-url}")
    private String searchUrl;

    @Value("${agefiph.api.base-url}")
    private String baseUrl;

    private static final String AUTH_TOKEN_COOKIE = "company.security.authtoken";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    private static final String COOKIE = "Cookie";
    private static final String CANDIDATE_API_PATH = "/api/company/candidates/%s";
    private static final String BUILD_SEARCH_PAYLOAD = """
            {
                "wishedLocationFilter": false,
                "addressLocationFilter": true,
                "includeSynonyms": true,
                "includeCandidatesViewedByUser": true,
                "includeCandidatesUnViewedByUser": true,
                "includeCandidatesWithoutSalary": true,
                "includeAvailabilityDate": false,
                "customFieldItems": {},
                "queryType": "MATCHING",
                "query": "",
                "locations": [
                    {
                        "geonameId": 2996944,
                        "radius": 30,
                        "admin1": 11071625,
                        "admin2": 2987410,
                        "admin3": 6454573,
                        "country": 3017382,
                        "label": "Lyon (69)",
                        "centerLat": 45.74846,
                        "centerLon": 4.84671
                    }
                ],
                "filters": [
                    {
                        "key": "lastLogin_agg",
                        "values": ["lastLogin_from_now-24M_to_now"]
                    }
                ],
                "facets": [],
                "matching": {
                    "minMatch": 0,
                    "experienceJobs": [],
                    "specializations": [],
                    "accreditationMainIds": [],
                    "languages": [],
                    "degrees": [],
                    "contractTypes": [],
                    "jobTypes": [],
                    "licenses": [],
                    "industryFields": []
                }
            }
            """;

    /**
     * Auth token retrieval instructions:
     * 1. Navigate to https://espace-emploi.agefiph.fr/company/signin
     * 2. Login with email: <EMAIL>
     * 3. Get token either:
     * a) From DevTools > Network > find /login request > Headers > set-cookie > company.security.authtoken
     * b) From DevTools > Application >COOKIE > agefiph > company.security.authtoken
     * 4. Store this token in the configurableProperty table with key "agefiph.auth.token"
     */
    private String getAuthToken() {
        var property = configurablePropertyRepository.findOneByPropertyKey("agefiph.auth.token");
        return property != null ? property.getPropertyValue() : null;
    }

    public List<ScrapedCandidate> searchCandidates() throws InvalidScrapingException {
        var authToken = validateAndGetAuthToken();

        try {
            log.debug("Searching candidates at URL: {}", searchUrl);

            var requestBody = RequestBody.create(BUILD_SEARCH_PAYLOAD, JSON);

            var request = new Request.Builder()
                    .url(searchUrl)
                    .post(requestBody)
                    .addHeader("Content-Type", "application/json")
                    .addHeader(COOKIE, AUTH_TOKEN_COOKIE + "=" + authToken)
                    .build();

            try (var response = retryableHttpClient.executeRequestWithStatusCheck(request)) {
                var responseBody = response.body().string();
                return parseCandidatesFromJsonResponse(responseBody);
            }
        } catch (RuntimeException | IOException e) {
            log.warn("Technical error during AGEFIPH API candidate search: {}", e.getMessage(), e);
            throw new InvalidScrapingException(e);
        }
    }

    private List<ScrapedCandidate> parseCandidatesFromJsonResponse(String responseBody) throws InvalidScrapingException {
        var candidates = new ArrayList<ScrapedCandidate>();

        try {
            var jsonNode = objectMapper.readTree(responseBody);
            var itemsNode = jsonNode.path("items");

            if (!itemsNode.isArray()) {
                log.warn("No items array found in AGEFIPH API response");
                return candidates;
            }

            log.debug("Found {} candidate items in AGEFIPH API response", itemsNode.size());

            StreamSupport.stream(itemsNode.spliterator(), false)
                    .map(this::parseCandidateFromJsonNode)
                    .filter(Objects::nonNull)
                    .forEach(candidates::add);

            log.info("Successfully parsed {} scraped candidates from AGEFIPH API response", candidates.size());
        } catch (JsonProcessingException e) {
            log.error("Error parsing scraped candidates from AGEFIPH API response", e);
            throw new InvalidScrapingException("Failed to parse JSON response", e);
        }

        return candidates;
    }

    private ScrapedCandidate parseCandidateFromJsonNode(JsonNode candidateNode) {
        try {
            var candidateId = candidateNode.path("id").asText();
            if (candidateId.isEmpty()) {
                log.debug("Skipping candidate with empty ID");
                return null;
            }

            var email = candidateNode.path("email").asText();
            var hasResume = candidateNode.path("hasResume").asBoolean();


            var cvDownloadLink = baseUrl + CANDIDATE_API_PATH.formatted(candidateId) + "/resume/download";
            var profileUrl = baseUrl + CANDIDATE_API_PATH.formatted(candidateId);

            log.debug("Parsed candidate - ID: {}, Email: {}", candidateId, email);

            return ScrapedCandidate.builder()
                    .candidateId(candidateId)
                    .email(email)
                    .hasResume(hasResume)
                    .cvDownloadLink(cvDownloadLink)
                    .profileUrl(profileUrl)
                    .build();
        } catch (RuntimeException e) {
            log.error("Error parsing candidate from JSON node: {}", e.getMessage(), e);
            return null;
        }
    }

    public ScrapedCandidate getCandidateDetails(String candidateId) throws InvalidScrapingException {
        var authToken = validateAndGetAuthToken();

        try {
            var detailsUrl = baseUrl + CANDIDATE_API_PATH.formatted(candidateId);
            log.debug("Fetching candidate details from URL: {}", detailsUrl);

            var request = new Request.Builder()
                    .url(detailsUrl)
                    .get()
                    .addHeader("Content-Type", "application/json")
                    .addHeader(COOKIE, AUTH_TOKEN_COOKIE + "=" + authToken)
                    .build();

            try (var response = retryableHttpClient.executeRequestWithStatusCheck(request)) {
                var responseBody = response.body().string();
                return parseCandidateDetailsFromJson(responseBody);
            }
        } catch (RuntimeException | IOException e) {
            log.warn("Technical error during AGEFIPH API candidate details fetch: {}", e.getMessage(), e);
            throw new InvalidScrapingException(e);
        }
    }

    private ScrapedCandidate parseCandidateDetailsFromJson(String responseBody) throws InvalidScrapingException {
        try {
            var detailsNode = objectMapper.readTree(responseBody);

            var candidateId = detailsNode.path("id").asText();
            var firstName = detailsNode.path("firstName").asText();
            var lastName = detailsNode.path("name").asText();
            var email = detailsNode.path("login").asText();

            var addressLocationNode = detailsNode.path("addressLocation");
            var location = addressLocationNode.path("label").asText();
            var jobTitle = extractJobTitle(detailsNode);

            var cvDownloadLink = baseUrl + CANDIDATE_API_PATH.formatted(candidateId) + "/resume/download";
            var profileUrl = baseUrl + CANDIDATE_API_PATH.formatted(candidateId);

            log.debug("Parsed candidate details - ID: {}, Name: {} {}, Email: {}, Job Title: {}, Location: {}",
                    candidateId, firstName, lastName, email, jobTitle, location);

            return ScrapedCandidate.builder()
                    .candidateId(candidateId)
                    .firstName(firstName)
                    .lastName(lastName)
                    .email(email)
                    .jobTitle(jobTitle)
                    .location(location)
                    .cvDownloadLink(cvDownloadLink)
                    .profileUrl(profileUrl)
                    .build();
        } catch (JsonProcessingException e) {
            log.error("Error parsing candidate details from AGEFIPH API response", e);
            throw new InvalidScrapingException("Failed to parse candidate details JSON response", e);
        }
    }

    private String extractJobTitle(JsonNode candidateNode) {
        var jobsNode = candidateNode.path("jobs");
        if (jobsNode.isArray() && !jobsNode.isEmpty()) {
            return jobsNode.get(0).path("value").asText();
        }
        return "";
    }

    private String validateAndGetAuthToken() throws InvalidScrapingException {
        var authToken = getAuthToken();
        if (StringUtils.isBlank(authToken)) {
            log.warn("AGEFIPH auth token not configured in database");
            throw new InvalidScrapingException("AGEFIPH auth token not configured");
        }
        return authToken;
    }
}
