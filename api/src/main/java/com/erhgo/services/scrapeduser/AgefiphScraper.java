package com.erhgo.services.scrapeduser;

import com.erhgo.domain.exceptions.InvalidScrapingException;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.services.http.RetryableHttpClient;
import com.erhgo.services.scrapeduser.dto.ScrapedCandidate;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Element;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class AgefiphScraper {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final RetryableHttpClient retryableHttpClient;
    private final ConfigurablePropertyRepository configurablePropertyRepository;

    @Value("${agefiph.api.search-url}")
    private String searchUrl;

    @Value("${agefiph.api.search-query-params}")
    private String searchQueryParams;

    @Value("${agefiph.api.base-url}")
    private String baseUrl;

    private static final String CV_DOWNLOAD_PATH = "/company/candidate/search/resume/download/";
    private static final String CANDIDATE_DETAIL_PATH = "/company/candidate/search/candidate/";
    private static final String AUTH_TOKEN_COOKIE = "company.security.authtoken";

    /**
     * Auth token retrieval instructions:
     * 1. Navigate to https://espace-emploi.agefiph.fr/company/signin
     * 2. Login with email: <EMAIL>
     * 3. Get token either:
     * a) From DevTools > Network > find /login request > Headers > set-cookie > company.security.authtoken
     * b) From DevTools > Application > Cookies > agefiph > company.security.authtoken
     * 4. Store this token in the configurableProperty table with key "agefiph.auth.token"
     */
    private String getAuthToken() {
        var property = configurablePropertyRepository.findOneByPropertyKey("agefiph.auth.token");
        return property != null ? property.getPropertyValue() : null;
    }

    public List<ScrapedCandidate> searchCandidates() throws InvalidScrapingException {
        var authToken = getAuthToken();
        if (StringUtils.isBlank(authToken)) {
            log.warn("AGEFIPH auth token not configured in database");
            return new ArrayList<>();
        }

        try {
            var fullUrl = searchUrl + "?" + searchQueryParams;
            log.debug("Searching candidates at URL: {}", fullUrl);

            var request = new Request.Builder()
                    .url(fullUrl)
                    .get()
                    .addHeader("Accept", "application/json")
                    .addHeader("Cookie", AUTH_TOKEN_COOKIE + "=" + authToken)
                    .build();

            try (var response = retryableHttpClient.executeRequestWithStatusCheck(request)) {
                var responseBody = response.body().string();
                return parseCandidatesFromResponse(responseBody);
            }
        } catch (RuntimeException | IOException e) {
            log.warn("Technical error during AGEFIPH API candidate search: {}", e.getMessage(), e);
            throw new InvalidScrapingException(e);
        }
    }

    public byte @Nullable [] downloadCv(String cvDownloadLink) throws InvalidScrapingException {
        var authToken = getAuthToken();
        if (StringUtils.isBlank(authToken)) {
            log.warn("AGEFIPH auth token not configured in database");
            return null;
        }

        log.info("Downloading CV file from URL: {}", cvDownloadLink);
        if (org.apache.commons.lang3.StringUtils.isBlank(cvDownloadLink)) {
            log.warn("No file URL set on AGEFIPH API CV download - received empty link");
            return null;
        }

        try {
            var request = new Request.Builder()
                    .url(cvDownloadLink)
                    .get()
                    .addHeader("Cookie", AUTH_TOKEN_COOKIE + "=" + authToken)
                    .build();

            try (var response = retryableHttpClient.executeRequestWithStatusCheck(request)) {
                var fileData = response.body().bytes();
                log.debug("Successfully downloaded file from URL: {}, size: {} bytes", request.url(), fileData.length);
                return fileData.length == 0 ? null : fileData;
            }
        } catch (IOException | RuntimeException e) {
            log.error("IO error during AGEFIPH API CV download ; URL:{}, {}", cvDownloadLink, e.getMessage(), e);
            return null;
        }
    }


    private List<ScrapedCandidate> parseCandidatesFromResponse(String responseBody) throws InvalidScrapingException {
        var candidates = new ArrayList<ScrapedCandidate>();

        try {
            var jsonNode = objectMapper.readTree(responseBody);
            var resultsHtml = jsonNode.path("results").asText();

            if (resultsHtml.isEmpty()) {
                log.warn("No results HTML found in AGEFIPH API response");
                return candidates;
            }

            var doc = Jsoup.parse(resultsHtml);
            var candidateElements = doc.select("div.candidate-li[data-candidate-id]");

            log.debug("Found {} scraped candidate elements in HTML AGEFIPH API response", candidateElements.size());

            candidateElements.stream()
                    .map(this::parseCandidateElement)
                    .filter(Objects::nonNull)
                    .forEach(candidates::add);

            log.info("Successfully parsed {} scraped candidates from AGEFIPH API response", candidates.size());
        } catch (JsonProcessingException e) {
            log.error("Error parsing scraped candidates from AGEFIPH API response", e);
        }

        return candidates.stream()
                .filter(candidate -> StringUtils.isNotEmpty(candidate.getCvDownloadLink()))
                .toList();
    }

    private ScrapedCandidate parseCandidateElement(Element candidateElement) {
        var candidateId = candidateElement.attr("data-candidate-id");
        try {
            if (candidateId.isEmpty()) {
                log.debug("Skipping scraped candidate element with empty candidateId");
                return null;
            }

            var nameElement = candidateElement.select("a.link-ninja").first();
            var name = nameElement != null ? nameElement.text().trim() : "Candidat anonyme";

            var jobTitle = extractJobTitle(candidateElement);
            var location = extractLocation(candidateElement);
            var cvDownloadLink = extractCvDownloadLink(candidateElement);
            var profileUrl = baseUrl + CANDIDATE_DETAIL_PATH + candidateId;

            log.debug("Parsed scraped candidate - ID: {}, Name: {}, Job Title: {}, Location: {}, CV Link: {}, Profile URL: {}",
                    candidateId, name, jobTitle, location, cvDownloadLink, profileUrl);

            return ScrapedCandidate.builder()
                    .candidateId(candidateId)
                    .name(name)
                    .cvDownloadLink(cvDownloadLink)
                    .jobTitle(jobTitle)
                    .location(location)
                    .profileUrl(profileUrl)
                    .build();
        } catch (RuntimeException e) {
            log.error("Error parsing candidate element with ID {}: {}", candidateId, e.getMessage(), e);
            return null;
        }
    }

    private String extractCvDownloadLink(Element candidateElement) {
        var cvLinkElement = candidateElement.select("a[href*='" + CV_DOWNLOAD_PATH + "']").first();
        if (cvLinkElement != null) {
            var href = cvLinkElement.attr("href");
            return baseUrl + href;
        }
        return null;
    }

    private String extractJobTitle(Element candidateElement) {
        var jobTitleElement = candidateElement.select("div:contains(Recherche un poste) b").first();
        if (jobTitleElement != null) {
            return jobTitleElement.text().trim();
        }
        return null;
    }

    private String extractLocation(Element candidateElement) {
        var locationElement = candidateElement.select("li:has(i.fa-home)").first();
        if (locationElement != null) {
            var locationText = locationElement.text().trim();
            return locationText.replaceAll("^\\s*", "").trim();
        }
        return null;
    }
}
