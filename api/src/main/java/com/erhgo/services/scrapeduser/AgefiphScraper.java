package com.erhgo.services.scrapeduser;

import com.erhgo.domain.exceptions.InvalidScrapingException;
import com.erhgo.repositories.ConfigurablePropertyRepository;
import com.erhgo.services.http.RetryableHttpClient;
import com.erhgo.services.scrapeduser.dto.ScrapedCandidate;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class AgefiphScraper {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private final RetryableHttpClient retryableHttpClient;
    private final ConfigurablePropertyRepository configurablePropertyRepository;

    @Value("${agefiph.api.search-url}")
    private String searchUrl;

    @Value("${agefiph.api.base-url}")
    private String baseUrl;

    private static final String AUTH_TOKEN_COOKIE = "company.security.authtoken";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    private static final String BUILD_

    /**
     * Auth token retrieval instructions:
     * 1. Navigate to https://espace-emploi.agefiph.fr/company/signin
     * 2. Login with email: <EMAIL>
     * 3. Get token either:
     * a) From DevTools > Network > find /login request > Headers > set-cookie > company.security.authtoken
     * b) From DevTools > Application > Cookies > agefiph > company.security.authtoken
     * 4. Store this token in the configurableProperty table with key "agefiph.auth.token"
     */
    private String getAuthToken() {
        var property = configurablePropertyRepository.findOneByPropertyKey("agefiph.auth.token");
        return property != null ? property.getPropertyValue() : null;
    }

    public List<ScrapedCandidate> searchCandidates() throws InvalidScrapingException {
        var authToken = getAuthToken();
        if (StringUtils.isBlank(authToken)) {
            log.warn("AGEFIPH auth token not configured in database");
            return new ArrayList<>();
        }

        try {
            log.debug("Searching candidates at URL: {}", searchUrl);

            var jsonPayload = buildSearchPayload();
            var requestBody = RequestBody.create(jsonPayload, JSON);

            var request = new Request.Builder()
                    .url(searchUrl)
                    .post(requestBody)
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Cookie", AUTH_TOKEN_COOKIE + "=" + authToken)
                    .build();

            try (var response = retryableHttpClient.executeRequestWithStatusCheck(request)) {
                var responseBody = response.body().string();
                return parseCandidatesFromJsonResponse(responseBody);
            }
        } catch (RuntimeException | IOException e) {
            log.warn("Technical error during AGEFIPH API candidate search: {}", e.getMessage(), e);
            throw new InvalidScrapingException(e);
        }
    }

    public byte @Nullable [] downloadCv(String cvDownloadLink) throws InvalidScrapingException {
        var authToken = getAuthToken();
        if (StringUtils.isBlank(authToken)) {
            log.warn("AGEFIPH auth token not configured in database");
            return null;
        }

        log.info("Downloading CV file from URL: {}", cvDownloadLink);
        if (org.apache.commons.lang3.StringUtils.isBlank(cvDownloadLink)) {
            log.warn("No file URL set on AGEFIPH API CV download - received empty link");
            return null;
        }

        try {
            var request = new Request.Builder()
                    .url(cvDownloadLink)
                    .get()
                    .addHeader("Cookie", AUTH_TOKEN_COOKIE + "=" + authToken)
                    .build();

            try (var response = retryableHttpClient.executeRequestWithStatusCheck(request)) {
                var fileData = response.body().bytes();
                log.debug("Successfully downloaded file from URL: {}, size: {} bytes", request.url(), fileData.length);
                return fileData.length == 0 ? null : fileData;
            }
        } catch (IOException | RuntimeException e) {
            log.error("IO error during AGEFIPH API CV download ; URL:{}, {}", cvDownloadLink, e.getMessage(), e);
            return null;
        }
    }


    private String buildSearchPayload() {
        return """
                {
                    "wishedLocationFilter": false,
                    "addressLocationFilter": true,
                    "includeSynonyms": true,
                    "includeCandidatesViewedByUser": true,
                    "includeCandidatesUnViewedByUser": true,
                    "includeCandidatesWithoutSalary": true,
                    "includeAvailabilityDate": false,
                    "customFieldItems": {},
                    "queryType": "MATCHING",
                    "query": "",
                    "locations": [
                        {
                            "geonameId": 2996944,
                            "radius": 30,
                            "admin1": 11071625,
                            "admin2": 2987410,
                            "admin3": 6454573,
                            "country": 3017382,
                            "label": "Lyon (69)",
                            "centerLat": 45.74846,
                            "centerLon": 4.84671
                        }
                    ],
                    "filters": [
                        {
                            "key": "lastLogin_agg",
                            "values": ["lastLogin_from_now-24M_to_now"]
                        }
                    ],
                    "facets": [],
                    "matching": {
                        "minMatch": 0,
                        "experienceJobs": [],
                        "specializations": [],
                        "accreditationMainIds": [],
                        "languages": [],
                        "degrees": [],
                        "contractTypes": [],
                        "jobTypes": [],
                        "licenses": [],
                        "industryFields": []
                    }
                }
                """;
    }

    private List<ScrapedCandidate> parseCandidatesFromJsonResponse(String responseBody) throws InvalidScrapingException {
        var candidates = new ArrayList<ScrapedCandidate>();

        try {
            var jsonNode = objectMapper.readTree(responseBody);
            var itemsNode = jsonNode.path("items");

            if (!itemsNode.isArray()) {
                log.warn("No items array found in AGEFIPH API response");
                return candidates;
            }

            log.debug("Found {} candidate items in AGEFIPH API response", itemsNode.size());

            for (var candidateNode : itemsNode) {
                var candidate = parseCandidateFromJsonNode(candidateNode);
                if (candidate != null) {
                    candidates.add(candidate);
                }
            }

            log.info("Successfully parsed {} scraped candidates from AGEFIPH API response", candidates.size());
        } catch (JsonProcessingException e) {
            log.error("Error parsing scraped candidates from AGEFIPH API response", e);
            throw new InvalidScrapingException("Failed to parse JSON response", e);
        }

        return candidates.stream()
                .filter(candidate -> candidate.getCvDownloadLink() != null)
                .toList();
    }

    private ScrapedCandidate parseCandidateFromJsonNode(JsonNode candidateNode) {
        try {
            var candidateId = candidateNode.path("id").asText();
            if (candidateId.isEmpty()) {
                log.debug("Skipping candidate with empty ID");
                return null;
            }

            // Only process candidates with CV
            var hasResume = candidateNode.path("hasResume").asBoolean(false);
            if (!hasResume) {
                log.debug("Skipping candidate {} without resume", candidateId);
                return null;
            }

            var firstName = candidateNode.path("firstName").asText();
            var lastName = candidateNode.path("name").asText();
            var email = candidateNode.path("email").asText();
            var title = candidateNode.path("title").asText();

            var addressLocationNode = candidateNode.path("addressLocation");
            var location = addressLocationNode.path("label").asText();

            var cvDownloadLink = baseUrl + "/api/company/candidates/" + candidateId + "/resume/download";
            var profileUrl = baseUrl + "/api/company/candidates/" + candidateId;

            log.debug("Parsed candidate - ID: {}, Name: {} {}, Email: {}, Title: {}, Location: {}",
                    candidateId, firstName, lastName, email, title, location);

            var candidate = ScrapedCandidate.builder()
                    .candidateId(candidateId)
                    .firstName(firstName)
                    .lastName(lastName)
                    .email(email)
                    .jobTitle(title)
                    .location(location)
                    .cvDownloadLink(cvDownloadLink)
                    .profileUrl(profileUrl)
                    .build();

            // Fetch detailed job title if not available in search results
            if (StringUtils.isBlank(title)) {
                enrichCandidateWithDetails(candidate);
            }

            return candidate;
        } catch (RuntimeException e) {
            log.error("Error parsing candidate from JSON node: {}", e.getMessage(), e);
            return null;
        }
    }

    private void enrichCandidateWithDetails(ScrapedCandidate candidate) {
        try {
            var authToken = getAuthToken();
            if (StringUtils.isBlank(authToken)) {
                log.warn("AGEFIPH auth token not configured, skipping candidate details enrichment");
                return;
            }

            var detailsUrl = baseUrl + "/api/company/candidates/" + candidate.getCandidateId();
            var request = new Request.Builder()
                    .url(detailsUrl)
                    .get()
                    .addHeader("Content-Type", "application/json")
                    .addHeader("Cookie", AUTH_TOKEN_COOKIE + "=" + authToken)
                    .build();

            try (var response = retryableHttpClient.executeRequestWithStatusCheck(request)) {
                var responseBody = response.body().string();
                var detailsNode = objectMapper.readTree(responseBody);

                var jobsNode = detailsNode.path("jobs");
                if (jobsNode.isArray() && jobsNode.size() > 0) {
                    var firstJob = jobsNode.get(0);
                    var jobTitle = firstJob.path("value").asText();
                    if (StringUtils.isNotBlank(jobTitle)) {
                        candidate.setJobTitle(jobTitle);
                        log.debug("Enriched candidate {} with job title: {}", candidate.getCandidateId(), jobTitle);
                    }
                }
            }
        } catch (RuntimeException | IOException e) {
            log.warn("Failed to enrich candidate {} with details: {}", candidate.getCandidateId(), e.getMessage());
        }
    }
}
